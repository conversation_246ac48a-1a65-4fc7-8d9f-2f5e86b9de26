import {createStackNavigator} from '@react-navigation/stack';
import React from 'react';
import RolePlay from '../screens/main/RolePlay.tsx';
import Home from '../screens/main/Home.tsx';
import Lesson from '../screens/main/Lesson.tsx';
import Profile from '../screens/main/Profile.tsx';
import Questions from '../screens/main/Questions.tsx';
import Unit from '../screens/main/Unit.tsx';
import {APP_SCREEN, RootStackParamList} from './screenType.ts';
import {MySchool} from '../screens/main/MySchool.tsx';
import LeaderBoard from '../screens/main/LeaderBoard.tsx';
import Practice from '../screens/main/Practice.tsx';
import {YourClass} from '../screens/main/YourClass.tsx';
import {Notification} from '../screens/main/Notification.tsx';
import ClassLeaderBoard from '../screens/main/ClassLeaderBoard.tsx';
import {PlayGround} from '../screens/main/PlayGround.tsx';
import Settings from '../screens/main/settings/Settings.tsx';
import {useTypedSelector} from '../redux/store.ts';
import {TopicCard} from '../screens/main/TopicCard.tsx';
import {FullConversation} from '../screens/main/FullConversation.tsx';
import EditProfile from '../screens/main/settings/EditProfile.tsx';
import ChangePassword from '../screens/main/settings/ChangePassword.tsx';
import NotificationSetting from '../screens/main/settings/NotiSetting.tsx';

const AppStack = createStackNavigator<RootStackParamList>();

const AppNavigator = () => {
  const isBgsound = useTypedSelector(state => state.profile.isPlayBgSound);

  // useEffect(() => {
  //   if (!isBgsound) return;
  //   backgroundMusicManager.play('cheppymussic');
  //   return () => backgroundMusicManager.stop();
  // }, [isBgsound]);

  return (
    <AppStack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: false,
        detachPreviousScreen: false,
      }}>
      <AppStack.Screen
        name={APP_SCREEN.HOME}
        component={Home}
        options={{
          animation: 'scale_from_center',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.PROFILE}
        component={Profile}
        options={{
          animation: 'scale_from_center',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.UNIT}
        component={Unit}
        options={{
          animation: 'scale_from_center',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.ROLE_PLAY}
        component={RolePlay}
        options={{
          animation: 'scale_from_center',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.QUESTIONS}
        component={Questions}
        options={{
          animation: 'scale_from_center',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.LESSON}
        component={Lesson}
        options={{
          animation: 'scale_from_center',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.MY_SCHOOL}
        component={MySchool}
        options={{
          animation: 'scale_from_center',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.YOUR_CLASS}
        component={YourClass}
        options={{
          animation: 'scale_from_center',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.NOTIFICATION}
        component={Notification}
        options={{
          animation: 'scale_from_center',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.LEADERBOARD}
        component={LeaderBoard}
        options={{
          animation: 'fade',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.PRACTICE}
        component={Practice}
        options={{
          animation: 'fade',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.PLAY_GROUND}
        component={PlayGround}
        options={{
          animation: 'scale_from_center',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.CLASS_LEADERBOARD}
        component={ClassLeaderBoard}
        options={{
          animation: 'scale_from_center',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.TOPIC_CARD}
        component={TopicCard}
        options={{
          animation: 'scale_from_center',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.SETTINGS}
        component={Settings}
        options={{
          animation: 'scale_from_center',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.FULL_CONVERSATION}
        component={FullConversation}
        options={{
          animation: 'scale_from_center',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.EDIT_PROFILE}
        component={EditProfile}
        options={{
          animation: 'scale_from_center',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.CHANGE_PASSWORD}
        component={ChangePassword}
        options={{
          animation: 'scale_from_center',
        }}
      />
      <AppStack.Screen
        name={APP_SCREEN.NOTIFICATION_SETTING}
        component={NotificationSetting}
        options={{
          animation: 'scale_from_center',
        }}
      />
    </AppStack.Navigator>
  );
};

export default AppNavigator;
