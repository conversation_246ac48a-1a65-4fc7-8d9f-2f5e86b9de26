import React from 'react';
import {
  ImageBackground,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import {moderateVerticalScale, scale} from 'react-native-size-matters';
import TextApp from '../TextApp';
import {Theme} from '../../themes';
import {useTypedSelector} from '../../redux/store';

interface RolePlayContentModalProps {
  modalContent: RolePlayCard | null;
  onClose: () => void;
  onStartNow: () => void;
}

export const RolePlayContentModal: React.FC<RolePlayContentModalProps> = ({
  modalContent,
  onClose,
  onStartNow,
}) => {
  const {data} = useTypedSelector(state => state.profile);
  return (
    <ImageBackground
      style={styles.container}
      source={Theme.images.bgConfirmChooseCard}
      resizeMode="stretch">
      <TouchableOpacity style={styles.closeButton} onPress={onClose}>
        <FastImage
          source={Theme.images.closeContentRolePlay}
          style={styles.closeIcon}
          resizeMode="contain"
        />
      </TouchableOpacity>

      <TextApp
        text={modalContent?.title}
        preset="text_xl_semibold"
        textColor="#7C3603"
        style={styles.title}
      />

      <View style={styles.characterRow}>
        <View style={styles.characterBox}>
          <View style={styles.avatarCircle}>
            <FastImage
              source={{uri: data?.photoUrl}}
              style={styles.avatarImage}
              resizeMode="contain"
            />
          </View>
          <TextApp
            preset="text_xs_semibold"
            text={'You are:'}
            style={styles.characterName}
          />
          <TextApp
            preset="text_xs_regular"
            text={modalContent?.answerer}
            style={styles.characterRole}
          />
        </View>

        <View style={styles.characterBox}>
          <View style={[styles.avatarCircle, {backgroundColor: '#3A600D'}]}>
            <FastImage
              source={(Theme.images as any)[modalContent?.rlFigureCode || '']}
              style={styles.avatarImage}
              resizeMode="contain"
            />
          </View>
          <TextApp
            preset="text_xs_semibold"
            text={'Cheppy:'}
            style={styles.characterName}
          />
          <TextApp
            preset="text_xs_regular"
            text={modalContent?.questioner}
            style={styles.characterRole}
          />
        </View>
      </View>

      <TextApp
        preset="text_xs_regular"
        text={`Mission: ${modalContent?.missionSummary}`}
        style={styles.mission}
        textColor="#181D27"
      />

      <View style={styles.rewardBox}>
        <TextApp text={`Reward:`} style={styles.rewardText} />
        <TextApp
          preset="text_sm_bold"
          text={`${modalContent?.coin}`}
          style={styles.rewardText}
        />
        <FastImage source={Theme.icons.gem} style={styles.pearlIcon} />
      </View>

      <TouchableOpacity onPress={onStartNow}>
        <FastImage
          source={Theme.images.startNowRolePlay}
          style={styles.startNowButton}
          resizeMode="contain"
        />
      </TouchableOpacity>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 295.39,
    height: 465.79,
  },
  closeButton: {
    position: 'absolute',
    right: -5,
    top: -5,
    width: 25,
    height: 25,
    backgroundColor: '#D9D9D9',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeIcon: {
    width: 45,
    height: 45,
  },
  title: {
    textAlign: 'center',
    lineHeight: 32,
    marginTop: moderateVerticalScale(30),
  },
  characterRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 30,
    marginBottom: 20,
    marginTop: 16,
  },
  characterBox: {
    alignItems: 'center',
    flex: 1,
  },
  avatarCircle: {
    backgroundColor: '#843F00',
    width: 100,
    height: 100,
    borderRadius: 50,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
  },
  characterName: {
    lineHeight: 18,
    textAlign: 'center',
  },
  characterRole: {
    lineHeight: 18,
    textAlign: 'center',
  },
  mission: {
    textAlign: 'center',
    marginBottom: moderateVerticalScale(20),
    marginHorizontal: scale(30),
  },
  rewardBox: {
    width: 160,
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FCF3CA',
    paddingVertical: 10,
    borderRadius: 8,
  },
  rewardText: {
    marginRight: 8,
    lineHeight: 18,
  },
  pearlIcon: {
    width: 21,
    height: 21,
    resizeMode: 'contain',
  },
  startNowButton: {
    width: 197.96,
    height: 87.22,
    alignSelf: 'center',
  },
});
