import * as React from 'react';
import {Canvas, Group, Path} from '@shopify/react-native-skia';

type RopeRingRolePlayProps = {};

const RopeRingRolePlay = ({}: RopeRingRolePlayProps) => {
  const [completed, setCompleted] = React.useState(false);
  return (
    <Canvas style={{width: 82, height: 143}}>
      <Group>
        <Path
          path="M41.048 3.854a881.638 881.638 0 0 1-1.641 67.29c-.396 6.328-.861 12.65-1.391 18.961-.15 1.795 1.341 3.335 2.683 3.335 1.576 0 2.531-1.537 2.683-3.335a888.58 888.58 0 0 0 3.095-67.385 869.85 869.85 0 0 0-.06-18.866c-.061-4.288-5.427-4.302-5.367 0h-.002Z"
          color="#CE8900"
        />
        <Path
          path="M38.099 90.308c1.182-.411 2.325-1.003 3.307-1.931 1.232-1.162 2.148-2.72 2.87-4.407.204-.473-.375-.895-.579-.42-.675 1.578-1.527 3.076-2.687 4.16-.924.864-1.986 1.409-3.09 1.792-.416.145-.24.948.179.803v.003ZM39.102 80.599c1.058-1.034 2.033-2.21 2.939-3.446 1.075-1.47 2.079-3.07 2.797-4.855.192-.478-.387-.9-.58-.42-.706 1.757-1.688 3.324-2.752 4.769-.888 1.206-1.842 2.351-2.877 3.362-.34.331.138.918.474.59ZM39.619 69.8c.997-1.176 2.093-2.21 3.07-3.413 1.185-1.456 2.211-3.106 2.833-5.04.16-.498-.488-.715-.646-.223-.597 1.856-1.633 3.43-2.775 4.81-.948 1.148-1.999 2.146-2.958 3.274-.313.37.16.959.474.589l.002.003ZM40.455 59.601c2.435-3.07 4.497-6.647 5.724-10.701.152-.503-.496-.72-.646-.223-1.187 3.916-3.202 7.37-5.552 10.335-.304.384.17.973.474.59ZM41.086 46.443c.919-1.4 2.026-2.593 2.996-3.938 1.133-1.57 2.066-3.32 2.602-5.335.137-.511-.512-.731-.646-.222-.514 1.937-1.435 3.612-2.533 5.11-.944 1.287-2.006 2.443-2.891 3.793-.27.412.201 1.006.474.59l-.002.002ZM41.231 36.211c2.041-2.112 3.712-4.89 4.856-7.859a25.83 25.83 0 0 0 1.029-3.31c.123-.516-.525-.736-.646-.222-.83 3.494-2.408 6.706-4.438 9.324a17.67 17.67 0 0 1-1.275 1.478c-.333.345.145.931.474.59ZM41.374 24.993c2.292-3.163 4.537-6.52 5.507-10.688.121-.517-.527-.736-.646-.222-.937 4.026-3.121 7.267-5.335 10.32-.288.398.186.99.474.59ZM41.352 14.03c.577-.445.966-1.087 1.36-1.76.482-.825.972-1.641 1.428-2.489a63.195 63.195 0 0 0 2.498-5.241c.2-.475-.378-.895-.58-.42A63.14 63.14 0 0 1 43.68 9.14c-.436.82-.89 1.62-1.364 2.404-.39.647-.735 1.33-1.3 1.764-.367.281-.03 1.004.337.72v.003Z"
          color="#985300"
        />
        <Path
          path="M40.286 114.945c-7.235-.431-12.748-6.644-12.317-13.878.431-7.235 6.644-12.748 13.879-12.317 7.235.431 12.748 6.644 12.316 13.879-.43 7.234-6.643 12.748-13.878 12.316Zm1.757-29.482c-9.05-.54-16.824 6.36-17.36 15.409-.54 9.049 6.36 16.82 15.408 17.36 9.05.54 16.824-6.359 17.36-15.408.54-9.05-6.36-16.82-15.408-17.36Z"
          style="stroke"
          strokeWidth={8}
          color={completed ? '#FFFF00' : '#D37300'}
        />
        <Path
          path="M40.267 115.257c-7.408-.441-13.052-6.804-12.61-14.211.44-7.407 6.803-13.051 14.21-12.61 7.407.44 13.051 6.803 12.61 14.211-.44 7.407-6.803 13.051-14.21 12.61Zm1.801-30.185c-9.266-.552-17.223 6.51-17.775 15.776-.553 9.266 6.51 17.223 15.776 17.776 9.266.552 17.222-6.51 17.775-15.776.553-9.266-6.51-17.223-15.776-17.776Z"
          color={completed ? '#FFCE00' : '#DDD200'}
        />
        {completed && (
          <Group>
            <Path
              path="M41.151 112.924c6.116 0 11.074-4.958 11.074-11.074s-4.958-11.075-11.074-11.075-11.074 4.959-11.074 11.075 4.958 11.074 11.074 11.074Z"
              color="#68B900"
            />
            <Path
              path="M48.463 97.27c-.262.01-.62.258-.754.354-.019.013-.013.007.022-.022.068-.058.18-.16.211-.246.035-.195-.39.058-.488.067a.196.196 0 0 1-.147-.029c-.07-.028-.112-.016-.192.035-.584.476-1.118 1.042-1.703 1.514-.012.007-.006-.012.023-.047.048-.064.128-.157.185-.227.339-.422.728-.824 1.035-1.259.118-.22-.217-.099-.326-.054a3.038 3.038 0 0 0-1.172.818c-.08.08-.14.191-.246.236-.054 0-.029-.198-.137-.214-.109 0-.173.058-.281.125-1.962 1.405-3.565 3.207-4.67 5.232-.06.095-.217.46-.265.214a4.565 4.565 0 0 0-1.62-3.22c-.252-.22-.47-.272-.383.147.08.425.297.843.556 1.201.064.099-.182-.192-.288-.304-.15-.14-.51-.651-.635-.559-.08.08-.122.227-.16.323-.023.054-.029.096-.032.058-.003-.221-.367-.569-.591-.336-.077.096-.054.269-.077.361a.119.119 0 0 1-.057.083c-.054.032-.131.032-.176.07-.064.084.02.189.074.291.744 1.166 1.491 2.332 2.239 3.494l-.205.349c-.092.143-.156.316-.006.444.227.194.332.479.565.696.083.077.182.172.3.182.132 0 .211-.118.275-.22l.115.179c.051.083.106.156.163.21l.086.451c.016.08.035.185.07.249.007.013.017.019.023.019.032-.006.038-.051.048-.08.032-.099.064-.313.102-.418.083-.246.182.092.25.019.038-.077.038-.182.085-.256.042-.07.119-.095.176-.15.125-.099.051-.457.256-.281.035.026.067.045.067-.019a11.836 11.836 0 0 0-.09-.553 64.349 64.349 0 0 1 7.64-8.662c.097-.096.253-.246.135-.268v.003Z"
              color="#fff"
            />
          </Group>
        )}
      </Group>
    </Canvas>
  );
};
export default RopeRingRolePlay;
