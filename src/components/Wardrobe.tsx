import React, {useState} from 'react';
import {
  Image,
  ImageBackground,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import ListViewProfile, {GridItem} from './ListViewProfile.tsx';
import {useReduxDispatch, useTypedSelector} from '../redux/store.ts';
import {changeCharacter} from '../redux/reducer/ProfileSlice.ts';
import {isAndroid} from '../utils/Scale.ts';

export type ClothingCategory = 'shirt' | 'pants' | 'mask' | 'pet' | 'glasses';

export interface ClothingItem {
  id: string;
  category: ClothingCategory;
  image: any;
  selected?: boolean;
}

const tabs: {label: string; key: ClothingCategory; icon: any}[] = [
  {
    label: 'Shirt',
    key: 'shirt',
    icon: require('../../assets/images/profiles/shirrt.png'),
  },
  {
    label: 'Pants',
    key: 'pants',
    icon: require('../../assets/images/profiles/pants.png'),
  },

  {
    label: 'glasses',
    key: 'glasses',
    icon: require('../../assets/images/profiles/glasses.png'),
  },
  {
    label: 'Mask',
    key: 'mask',
    icon: require('../../assets/images/profiles/mask.png'),
  },
  {
    label: 'Pet',
    key: 'pet',
    icon: require('../../assets/images/profiles/pet.png'),
  },
];
const ShirtsData: GridItem[] = [
  {id: 1, image: require('../../assets/svg/profile/ao1.png'), type: 'shirt'},
  {id: 2, image: require('../../assets/svg/profile/ao2.png'), type: 'shirt'},
  {id: 3, image: require('../../assets/svg/profile/ao3.png'), type: 'shirt'},
  {id: 4, image: require('../../assets/svg/profile/ao1.png'), type: 'shirt'},
  {id: 5, image: require('../../assets/svg/profile/ao2.png'), type: 'shirt'},
  {id: 6, image: require('../../assets/svg/profile/ao3.png'), type: 'shirt'},
];
const PantsData: GridItem[] = [
  {id: 1, image: require('../../assets/svg/profile/quan1.png'), type: 'pants'},
  {id: 2, image: require('../../assets/svg/profile/quan2.png'), type: 'pants'},
  {id: 3, image: require('../../assets/svg/profile/quan1.png'), type: 'pants'},
  {id: 4, image: require('../../assets/svg/profile/quan2.png'), type: 'pants'},
  {id: 5, image: require('../../assets/svg/profile/quan1.png'), type: 'pants'},
  {
    id: 6,
    image: require('../../assets/svg/profile/quan2.png'),
    type: 'pants',
  },
];
const AccessorysData: GridItem[] = [
  {
    id: 1,
    image: require('../../assets/svg/profile/giay1.png'),
    type: 'accessory',
  },
  {
    id: 2,
    image: require('../../assets/svg/profile/giay2.png'),
    type: 'accessory',
  },
  {
    id: 3,
    image: require('../../assets/svg/profile/giay1.png'),
    type: 'accessory',
  },
  {
    id: 4,
    image: require('../../assets/svg/profile/giay2.png'),
    type: 'accessory',
  },
  {
    id: 5,
    image: require('../../assets/svg/profile/giay1.png'),
    type: 'accessory',
  },
  {
    id: 6,
    image: require('../../assets/svg/profile/giay2.png'),
    type: 'accessory',
  },
];
const FaceData: GridItem[] = [
  {
    id: 1,
    image: require('../../assets/svg/profile/face1.png'),
    type: 'face',
  },
  {
    id: 2,
    image: require('../../assets/svg/profile/face2.png'),
    type: 'face',
  },
  {
    id: 3,
    image: require('../../assets/svg/profile/face3.png'),
    type: 'face',
  },
  {
    id: 4,
    image: require('../../assets/svg/profile/face4.png'),
    type: 'face',
  },
];
const PetData: GridItem[] = [
  {
    id: 1,
    image: require('../../assets/svg/profile/pet1.png'),
    type: 'pets',
  },
  {
    id: 2,
    image: require('../../assets/svg/profile/pet2.png'),
    type: 'pets',
  },
  {
    id: 3,
    image: require('../../assets/svg/profile/pet3.png'),
    type: 'pets',
  },
  {
    id: 4,
    image: require('../../assets/svg/profile/pet4.png'),
    type: 'pets',
  },
];
const Wardrobe: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState<ClothingCategory>('shirt');
  const character = useTypedSelector(state => state.profile.character);
  const dispatch = useReduxDispatch();
  const handleChange = (type: ClothingCategory, id: number) => {
    if (type === 'shirt') {
      dispatch(changeCharacter({...character, shirtId: id}));
    }
    if (type === 'pants') {
      dispatch(changeCharacter({...character, pantsId: id}));
    }
    if (type === 'glasses') {
      dispatch(changeCharacter({...character, accessoryId: id}));
    }
    if (type === 'mask') {
      dispatch(changeCharacter({...character, faceId: id}));
    }
    if (type === 'pet') {
      dispatch(changeCharacter({...character, petsId: id}));
    }
  };
  return (
    <View style={styles.container}>
      <ImageBackground
        source={require('../../assets/images/wardrobe.webp')}
        resizeMode={'stretch'}
        style={{
          flex: 1,
        }}>
        <View style={styles.tabs}>
          {tabs.map(tab => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.tabButton,
                selectedTab === tab.key && styles.tabButtonActive,
              ]}
              onPress={() => setSelectedTab(tab.key)}>
              <Image source={tab.icon} style={styles.tabIcon} />
            </TouchableOpacity>
          ))}
        </View>
        {selectedTab === 'shirt' && (
          <ListViewProfile
            data={ShirtsData}
            onSelect={item => handleChange(selectedTab, item.id)}
            id={character.shirtId}
          />
        )}
        {selectedTab === 'pants' && (
          <ListViewProfile
            data={PantsData}
            onSelect={item => handleChange(selectedTab, item.id)}
            id={character.pantsId}
          />
        )}
        {selectedTab === 'glasses' && (
          <ListViewProfile
            data={AccessorysData}
            onSelect={item => handleChange(selectedTab, item.id)}
            id={character.accessoryId}
          />
        )}
        {selectedTab === 'mask' && (
          <ListViewProfile
            data={FaceData}
            onSelect={item => handleChange(selectedTab, item.id)}
            id={character.faceId}
          />
        )}
        {selectedTab === 'pet' && (
          <ListViewProfile
            data={PetData}
            onSelect={item => handleChange(selectedTab, item.id)}
            id={character.petsId}
          />
        )}
      </ImageBackground>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,

    height: '100%',
  },
  tabs: {
    marginTop: isAndroid ? 6 : 8,
    flexDirection: 'row',

    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 12,
  },
  tabButton: {
    padding: 6,
    borderRadius: 8,
  },
  tabButtonActive: {
    // backgroundColor: '#FFD580',
    borderWidth: 3,
    borderColor: '#FFE576',
  },
  tabIcon: {
    width: isAndroid ? 40 : 48,
    height: isAndroid ? 40 : 48,
    resizeMode: 'contain',
  },
  grid: {
    padding: 10,
  },
  itemBox: {
    width: '30%',
    margin: '1.6%',
    backgroundColor: '#532B00',
    padding: 10,
    borderRadius: 10,
    alignItems: 'center',
  },
  itemSelected: {
    backgroundColor: '#FFDEAD',
    borderWidth: 2,
    borderColor: '#FF8612',
  },
  itemImage: {
    width: 60,
    height: 60,
    resizeMode: 'contain',
  },
});

export default Wardrobe;
