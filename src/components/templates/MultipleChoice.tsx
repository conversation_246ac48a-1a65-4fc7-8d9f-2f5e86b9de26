import React, {useCallback, useMemo, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import IconSpeak from '../../../assets/svgIcons/IconSpeak.tsx';
import useQuestion from '../../hooks/auth/useQuestion.ts';
import useSound from '../../hooks/useSound.ts';
import {useTheme} from '../../hooks/useTheme.ts';
import {Theme} from '../../themes';
import {getImages} from '../../utils/getImages.ts';
import {isAndroid, SCREEN_WIDTH} from '../../utils/Scale.ts';
import ButtonFooter from '../ButtonFooter.tsx';
import ItemQuestion from '../item/ItemQuestion.tsx';
import TextApp from '../TextApp';
import {chunkArray} from '../../utils/index.ts';
import {useTypedSelector} from '../../redux/store.ts';

interface ListenAndChooseTemplateProps {
  options: string[];
  title: string;
  answer: string;
  id: string;
  index: string | number;
  answerByStudent?: string;
  image: string;
  audio?: string;
  mediaType: 'AUDIO' | 'IMAGE';
  exerciseType: ExerciseType;
  question?: string;
}

const ITEM_WIDTH = scale(125);
const ITEM_HEIGHT = moderateVerticalScale(54);
const NUM_COLUMNS = 2;

const totalItemWidth = ITEM_WIDTH * NUM_COLUMNS;
const totalSpacing = SCREEN_WIDTH - totalItemWidth;
const spacing = totalSpacing / (NUM_COLUMNS + 1);

const MultipleChoice: React.FC<ListenAndChooseTemplateProps> = ({
  title,
  image,
  options,
  answer,
  id,
  index,
  answerByStudent,
  audio,
  mediaType,
  exerciseType,
  question,
}) => {
  const [selectedItem, setSelectedItem] = useState('');
  const [result, setResult] = useState(false);
  const {changeSource} = useSound();
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const {handleCheckAnswer, handleShowAnswer} = useQuestion();
  const isDone: boolean = useTypedSelector(state => state.question.isDone);
  const handlePressOption = useCallback((item: string) => {
    setSelectedItem(item);
  }, []);

  const isCorrectAnswer = selectedItem === answer;

  const handleSubmit = useCallback(() => {
    setResult(true);
    handleCheckAnswer(
      selectedItem,
      isCorrectAnswer,
      id,
      index,
      exerciseType,
      answer,
    );
  }, [selectedItem, answer, id, index, handleCheckAnswer]);

  const chunkedOptions = chunkArray(options, NUM_COLUMNS);

  return (
    <View
      style={[
        styles.container,
        {
          paddingTop: isDone
            ? moderateVerticalScale(isAndroid ? 130 : 150)
            : moderateVerticalScale(130),
        },
      ]}>
      <TextApp
        preset="text_md_semibold"
        textColor={theme.text_placeholder}
        style={{
          maxWidth: scale(300),
          textAlign: 'center',
          marginTop: isDone ? moderateVerticalScale(10) : 0,
        }}
        text={title}
      />
      <TextApp
        preset="text_lg_semibold"
        textColor={theme.text_secondary}
        style={{
          maxWidth: scale(300),
          textAlign: 'center',
          marginTop: 12,
        }}
        text={question}
      />
      <View style={styles.shadowWrapper}>
        <TouchableOpacity
          activeOpacity={1}
          disabled={mediaType !== 'AUDIO'}
          style={styles.boxMedia}
          onPress={() => changeSource(audio || '')}>
          {mediaType === 'AUDIO' ? (
            <IconSpeak />
          ) : (
            <FastImage
              resizeMode={'cover'}
              source={getImages(image, true)}
              defaultSource={Theme.images.boyAvt}
              style={styles.image}
            />
          )}
        </TouchableOpacity>
      </View>
      {chunkedOptions.map((row, rowIndex) => (
        <View key={rowIndex} style={styles.grid}>
          {row.map((item, colIndex) => {
            const idx = rowIndex * NUM_COLUMNS + colIndex;
            const isSelected = selectedItem === item;
            const isWrong = isSelected && result && item !== answer;
            const isAnsweredWrong =
              answerByStudent === item && answerByStudent !== answer;
            const isAnsweredCorrect =
              (answerByStudent === item && answerByStudent === answer) ||
              (isSelected && result && item === answer);

            return (
              <ItemQuestion
                key={idx}
                disable={result || isDone}
                onPress={() => handlePressOption(item)}
                text={item}
                isSelected={isSelected}
                isCorrect={isAnsweredCorrect}
                isWrong={isWrong || isAnsweredWrong}
                style={{
                  width: ITEM_WIDTH,
                  maxHeight: ITEM_HEIGHT,
                  marginLeft: colIndex === 0 ? spacing : spacing / 2,
                  marginRight:
                    colIndex === NUM_COLUMNS - 1 ? spacing : spacing / 2,
                }}
              />
            );
          })}
        </View>
      ))}

      {answerByStudent && answerByStudent !== answer && (
        <ButtonFooter
          btnCheck={handleShowAnswer?.bind(
            null,
            answerByStudent,
            exerciseType,
            answer,
          )}
          title="Show the answer"
        />
      )}

      {!answerByStudent && selectedItem && (
        <ButtonFooter
          title="Submit"
          disabled={!selectedItem}
          btnCheck={handleSubmit}
        />
      )}
      {isDone && (
        <View
          style={{
            height: 400,
            width: '100%',
            position: 'absolute',
            top: 360,
            zIndex: 30,
          }}
        />
      )}
    </View>
  );
};

export default MultipleChoice;

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      width: SCREEN_WIDTH,
      alignItems: 'center',
      paddingTop: moderateVerticalScale(130),
      backgroundColor: '#fff',
    },
    shadowWrapper: {
      shadowColor: '#0A0D121A',
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 1,
      shadowRadius: 3,
      elevation: 5,
    },
    boxMedia: {
      width: scale(200),
      height: scale(200),
      backgroundColor: '#FFFFFF',
      borderRadius: Theme.radius.radius_2xl,
      marginBottom: moderateVerticalScale(32),
      marginTop: moderateVerticalScale(20),
      alignItems: 'center',
      justifyContent: 'center',
      overflow: 'hidden',
    },
    image: {
      width: '100%',
      height: '100%',
    },
    grid: {
      flexDirection: 'row',
      justifyContent: 'flex-start',
    },
    item: {
      width: scale(120),
      height: verticalScale(44),
      marginBottom: 15,
      backgroundColor: theme.bg_primary,
      borderWidth: 1.5,
      borderColor: theme.fg_quaternary,
      borderRadius: Theme.radius.radius_md,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 2,
    },
    itemInner: {
      flex: 1,
      borderColor: '#FFFFFF',
      borderWidth: 2,
      width: '100%',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Theme.radius.radius_md,
    },
    selectedItem: {
      backgroundColor: theme.bg_brand_secondary,
      borderColor: theme.fg_brand_secondary,
    },
    correct: {
      backgroundColor: theme.bg_brand_secondary,
      borderColor: theme.fg_brand_secondary,
    },

    error: {
      backgroundColor: 'rgba(255, 0, 0, 0.5)',
      borderColor: '#FF0303',
    },
  });
