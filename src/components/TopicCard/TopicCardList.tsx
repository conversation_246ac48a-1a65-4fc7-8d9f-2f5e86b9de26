import React from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import {moderateVerticalScale, scale} from 'react-native-size-matters';
import {RolePlayCard} from '../RolePlay/RolePlayCard';
import {Theme} from '../../themes';
import {heightScreen} from '../../utils/Scale';
import TextApp from '../TextApp';

interface TopicCardListProps {
  topicCardData: any[];
  onChooseCard: (item: any) => void;
  //   isLoading?: boolean;
  //   handleRandomCard: () => void;
}

export const TopicCardList: React.FC<TopicCardListProps> = ({
  topicCardData,
  onChooseCard,
  //   isLoading,
  //   handleRandomCard,
}) => {
  //   if (isLoading) {
  //     return (
  //       <View style={styles.loadingView}>
  //         <ActivityIndicator color={'#fff'} size={'large'} />
  //       </View>
  //     );
  //   }

  return (
    <View style={styles.container}>
      <View style={styles.row}>
        {topicCardData.slice(0, 5).map((item, index) => (
          <TouchableOpacity
            key={index}
            style={styles.cardButton}
            onPress={onChooseCard?.bind(null, item)}>
            <FastImage
              source={(Theme.images as any)[`topicCard${index + 1}`]}
              style={styles.cardImage}
              resizeMode="contain"
            />
          </TouchableOpacity>
        ))}
      </View>
      <View style={styles.row}>
        <TouchableOpacity
          style={styles.cardButton}
          onPress={onChooseCard?.bind(null, topicCardData[5])}>
          <FastImage
            source={(Theme.images as any)[`topicCard6`]}
            style={styles.cardImage}
            resizeMode="contain"
          />
        </TouchableOpacity>
        <TouchableOpacity
          style={{
            width: 80,
            height: 80,
            marginHorizontal: 30,
          }}>
          <FastImage
            source={Theme.images.randomTopCard}
            style={styles.cardImage}
            resizeMode="contain"
          />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.cardButton}
          onPress={onChooseCard?.bind(null, topicCardData[6])}>
          <FastImage
            source={(Theme.images as any)[`topicCard7`]}
            style={styles.cardImage}
            resizeMode="contain"
          />
        </TouchableOpacity>
      </View>
      {/* <TouchableOpacity style={styles.extraCard} onPress={handleRandomCard}>
        <FastImage
          source={Theme.images.rolePlayCardCharactor5}
          style={styles.extraCardImage}
          resizeMode="contain"
        />
      </TouchableOpacity> */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    position: 'absolute',
    alignSelf: 'center',
    top: (483 / 812) * heightScreen,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingView: {
    position: 'absolute',
    bottom: (120 / 812) * heightScreen,
    alignSelf: 'center',
  },
  cardImage: {
    width: '100%',
    height: '100%',
  },
  cardButton: {
    width: 70,
    height: 100,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardButtonText: {
    fontSize: moderateVerticalScale(14),
    color: 'white',
    fontWeight: 'bold',
  },
});
