import React, {useState} from 'react';
import {
  ImageBackground,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {heightScreen, initTop, isAndroid} from '../../../utils/Scale.ts';
import {
  moderateVerticalScale,
  ms,
  verticalScale,
} from 'react-native-size-matters';

import Header from '../../../components/Header.tsx';
import TextApp from '../../../components/TextApp';
import Input from '../../../components/Input.tsx';
import ImageButton from '../../../components/ImageButton.tsx';
import Spacer from '../../../components/Spacer.tsx';

import {Theme} from '../../../themes';
import {useReduxDispatch, useTypedSelector} from '../../../redux/store.ts';
import {fetchUpdateProfile} from '../../../redux/reducer/fetchData.ts';

const EditProfile: React.FC = () => {
  const data = useTypedSelector(state => state.profile.data);
  const [name, setName] = useState(data?.name || '');
  const [email, setEmail] = useState(data?.email || '');
  const [phone, setPhone] = useState(data?.phoneNumber || '');
  const [birthday, setBirthday] = useState({day: '', month: '', year: ''});
  const [gender, setGender] = useState<1 | 2 | ''>(data?.gender || '');

  const dispatch = useReduxDispatch();
  const handleSubmit = () => {
    const payload = {
      name: name ?? data?.name,
      email: email ?? data?.email,
      phoneNumber: phone ?? data?.phoneNumber,
      birthday: birthday?.day
        ? `${birthday.day}-${birthday.month}-${birthday.year}`
        : data?.birthday,
      gender,
    };
    dispatch(
      fetchUpdateProfile({
        data: payload,
        id: data?.id,
      }),
    );
  };

  return (
    <ImageBackground
      resizeMode="stretch"
      source={require('../../../../assets/images/settings/bgsetting.webp')}
      style={styles.container}>
      <Header title="" />

      <TextApp
        text="Edit profile"
        preset="text_xl_semibold"
        textColor="#395500"
        style={styles.title}
      />

      <SafeAreaView style={styles.content}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <Input
            label="Username"
            presetLabel="text_md_semibold"
            inputStyle={styles.disabledInput}
            placeholder={data?.username}
            editable={false}
          />

          <Input
            label="Display name"
            presetLabel="text_md_semibold"
            inputStyle={styles.inputStyle}
            placeholder={data?.name}
            value={name}
            onChangeText={setName}
          />

          <Input
            label="Email"
            presetLabel="text_md_semibold"
            inputStyle={styles.inputStyle}
            placeholder={data?.email}
            value={email}
            onChangeText={setEmail}
          />

          <Input
            label="Mobile number"
            presetLabel="text_md_semibold"
            inputStyle={styles.inputStyle}
            placeholder={data?.phoneNumber}
            value={phone}
            onChangeText={setPhone}
          />

          <TextApp
            preset="text_md_semibold"
            text="My birthday"
            style={styles.label}
            textColor="#414651"
          />

          <View style={styles.containerInput}>
            <Input
              label="Day"
              inputStyle={styles.inputStyle}
              placeholder="DD"
              keyboardType={'numeric'}
              value={birthday.day}
              onChangeText={val => setBirthday(prev => ({...prev, day: val}))}
              styleContainer={{width: '30%'}}
              labelStyle={{textAlign: 'center'}}
            />
            <Input
              label="Month"
              inputStyle={styles.inputStyle}
              keyboardType={'numeric'}
              placeholder="MM"
              value={birthday.month}
              onChangeText={val => setBirthday(prev => ({...prev, month: val}))}
              styleContainer={{width: '30%'}}
              labelStyle={{textAlign: 'center'}}
            />
            <Input
              label="Year"
              keyboardType={'numeric'}
              inputStyle={styles.inputStyle}
              placeholder="YYYY"
              value={birthday.year}
              onChangeText={val => setBirthday(prev => ({...prev, year: val}))}
              styleContainer={{width: '30%'}}
              labelStyle={{textAlign: 'center'}}
            />
          </View>

          <TextApp
            preset="text_md_semibold"
            text="Gender"
            style={styles.label}
            textColor="#414651"
          />

          <View style={styles.containerInput}>
            {[1, 2].map(option => (
              <TouchableOpacity
                key={option}
                style={[
                  styles.btnGender,
                  gender === option && {backgroundColor: '#FF8612'},
                ]}
                onPress={() => setGender(option as 1 | 2)}>
                <TextApp
                  text={option === 1 ? 'Boy' : 'Girl'}
                  preset="text_md_regular"
                  textColor={(gender === option && '#FFFFFF') || undefined}
                  style={{textAlign: 'center'}}
                />
              </TouchableOpacity>
            ))}
          </View>

          <Spacer direction="vertical" size={38} />

          <ImageButton
            onPress={handleSubmit}
            title="Update"
            source={require('../../../../assets/images/btnUpdate.png')}
            widthVal="100%"
          />
        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: heightScreen + (isAndroid ? 0 : 130),
  },
  title: {
    position: 'absolute',
    textAlign: 'center',
    width: '100%',
    top: isAndroid ? verticalScale(38) : initTop + 6,
  },
  content: {
    flex: 1,
    paddingTop: 80,
    paddingHorizontal: 36,
  },
  inputStyle: {
    backgroundColor: '#FFFFFF',
  },
  disabledInput: {
    backgroundColor: '#F5F5F5',
  },
  label: {
    marginBottom: verticalScale(6),
    lineHeight: ms(18),
  },
  containerInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  btnGender: {
    height: moderateVerticalScale(44),
    backgroundColor: '#F5F5F5',
    width: '45%',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D5D7DA',
    borderRadius: Theme.radius.radius_md,
  },
});

export default EditProfile;
