import React from 'react';
import Header from '../../../components/Header.tsx';
import {ImageBackground, StyleSheet, View} from 'react-native';
import {
  heightScreen,
  initTop,
  isAndroid,
  widthScreen,
} from '../../../utils/Scale.ts';
import TextApp from '../../../components/TextApp';
import {
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import {KeyboardAwareScrollView} from 'react-native-keyboard-controller';
import Spacer from '../../../components/Spacer.tsx';
import Input from '../../../components/Input.tsx';
import Animated, {SlideInRight} from 'react-native-reanimated';
import {PasswordRule} from '../../../components/PasswordRule.tsx';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useForgotPassword} from '../../../hooks/unAuth/useForgotPassword.ts';
import {useTranslate} from '../../../hooks/useTranslate.ts';
import {useTheme} from '../../../hooks/useTheme.ts';
import {isValidEmail} from '../../../utils';
import ImageButton from '../../../components/ImageButton.tsx';

const ChangePassword: React.FC = () => {
  const {
    otpInputRef,
    status,
    formData,
    focused,
    errors,
    handleVerifyOTP,
    onChangeTextInput,
    onFocusInput,
    onBlurInput,
    handleSendOTPCode,
    handleReSendOTP,
    handleCreateNewPassword,
    rules,
    loading,
  } = useForgotPassword();
  const {t} = useTranslate();
  const theme = useTheme();

  const isDisabled = !formData?.email || !isValidEmail(formData.email);

  const isPasswordValid =
    rules?.validateLength && rules?.hasNumber && rules?.isMatch;

  const isDisabledResetPassword =
    !formData.password || !formData.confirmPassword || !isPasswordValid;

  return (
    <ImageBackground
      resizeMode={'stretch'}
      source={require('../../../../assets/images/settings/bgsetting.webp')}
      style={styles.container}>
      <Header title={''} />
      <TextApp
        text={'Change password'}
        preset={'text_xl_semibold'}
        textColor={'#395500'}
        style={{
          position: 'absolute',
          textAlign: 'center',
          width: '100%',
          top: isAndroid ? verticalScale(38) : initTop + 6,
        }}
      />
      <SafeAreaView style={styles.content}>
        <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
          <Animated.View
            key={status}
            entering={SlideInRight}
            style={styles.centerBox}>
            <Input
              label={t('forgot.createNewPassword.passwordLabel')}
              isPassword
              placeholder={t('forgot.createNewPassword.passwordPlaceholder')}
              value={formData?.password}
              onChangeText={val => onChangeTextInput('password', val)}
              onFocus={() => onFocusInput('password')}
              onBlur={() => onBlurInput('password')}
              error={t(errors.password)}
              inputStyle={{
                borderColor: errors.password
                  ? theme.border_error
                  : focused?.password
                    ? theme.border_brand
                    : theme.border_primary,
                backgroundColor: '#FFFFFF',
              }}
            />

            <Spacer size={5} />
            <Input
              label={t('forgot.createNewPassword.confirmPassword')}
              isPassword
              placeholder={t('forgot.createNewPassword.confirmPassword')}
              value={formData?.confirmPassword}
              onChangeText={val => onChangeTextInput('confirmPassword', val)}
              onFocus={() => onFocusInput('confirmPassword')}
              onBlur={() => onBlurInput('confirmPassword')}
              error={t(errors.confirmPassword)}
              inputStyle={{
                borderColor: errors.confirmPassword
                  ? theme.border_error
                  : focused?.confirmPassword
                    ? theme.border_brand
                    : theme.border_primary,
                backgroundColor: '#FFFFFF',
              }}
            />
            <Spacer size={5} />
            <PasswordRule
              validateLength={rules?.validateLength}
              hasNumber={rules?.hasNumber}
              isMatch={rules?.isMatch}
            />
          </Animated.View>
        </KeyboardAwareScrollView>
        {loading && <View style={styles.overlay} />}
        <View
          style={{
            position: 'absolute',
            width: widthScreen,
            justifyContent: 'center',

            bottom: 50,
          }}>
          <ImageButton
            onPress={() => {}}
            title="Update"
            source={require('../../../../assets/images/btnUpdate.png')}
            widthVal="100%"
          />
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: heightScreen + (isAndroid ? 0 : 130),
  },
  boxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
  },
  content: {
    flex: 1,
    paddingTop: 80,
    paddingHorizontal: 36,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: scale(16),
    marginTop: isAndroid ? verticalScale(5) : 0,
  },
  nameApp: {
    textAlign: 'center',
  },
  inputBox: {
    marginTop: moderateVerticalScale(32),
    marginHorizontal: scale(8),
  },
  mt: {
    height: moderateVerticalScale(48),
    borderWidth: 1,
  },
  centerBox: {
    paddingHorizontal: scale(16),
    paddingTop: verticalScale(30),
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'transparent',
    zIndex: 9999,
  },
  inputStyle: {
    backgroundColor: '#FFFFFF',
  },
});
export default ChangePassword;
