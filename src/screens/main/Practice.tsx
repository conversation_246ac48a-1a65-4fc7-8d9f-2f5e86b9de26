import {
  Canvas,
  Group,
  Image as SkiaImage,
  useImage,
} from '@shopify/react-native-skia';
import React, {useEffect, useRef} from 'react';
import {StyleSheet, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import Header from '../../components/Header.tsx';
import {Theme} from '../../themes/index.ts';
import {heightScreen, widthScreen} from '../../utils/Scale.ts';
import {useSharedValue, withTiming, cancelAnimation, useDerivedValue} from 'react-native-reanimated';

const Practice: React.FC = () => {
  const bannerImage = useImage(Theme.images.practiceHeaderBanner);
  const mistakeImage = useImage(Theme.images.practiceMistakeCard);
  const skillImage = useImage(Theme.images.practiceSkillCard);

  const bannerY = useSharedValue(-123.2);
  const cardScale = useSharedValue(0);
  const hasAnimated = useRef(false);

  const bannerTransform = useDerivedValue(() => [
    {translateY: bannerY.value}
  ], [bannerY]);

  const cardTransform = useDerivedValue(() => [
    {scale: cardScale.value}
  ], [cardScale]);

  useEffect(() => {
    if (hasAnimated.current) return;

    hasAnimated.current = true;

    bannerY.value = withTiming(0, {duration: 800}, (finished) => {
      if (finished) {
        cardScale.value = withTiming(1, {duration: 600});
      }
    });

    return () => {
      cancelAnimation(bannerY);
      cancelAnimation(cardScale);
      hasAnimated.current = false;
    };
  }, []);

  return (
    <View style={styles.container}>
      <Header />
      <FastImage
        source={Theme.images.bgPractice}
        style={styles.backgroundImage}
        resizeMode="stretch"
      />
      <Canvas style={styles.animationCanvas}>
        <Group transform={[{scale: widthScreen / 375}]}>
          <Group transform={bannerTransform}>
            <SkiaImage
              image={bannerImage}
              x={48.95}
              y={0}
              width={277}
              height={123.2}
            />
          </Group>

          <Group transform={cardTransform} origin={{x: 94.35, y: 361.5}}>
            <SkiaImage
              image={mistakeImage}
              x={25}
              y={257}
              width={138.7}
              height={209}
            />
          </Group>

          <Group
            transform={cardTransform}
            origin={{x: 280.08, y: 361.5}}>
            <SkiaImage
              image={skillImage}
              x={210.73}
              y={257}
              width={138.7}
              height={209}
            />
          </Group>
        </Group>
      </Canvas>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    position: 'absolute',
    width: widthScreen,
    height: heightScreen,
  },
  animationCanvas: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: 1,
  },
});

export default Practice;
