import React from 'react';
import {Image, ImageBackground, StyleSheet, View} from 'react-native';
import {
  moderateScale,
  moderateVerticalScale,
  verticalScale,
} from 'react-native-size-matters';
import {SvgIcons} from '../../../assets/svg';
import Header from '../../components/Header';
import {useTypedSelector} from '../../redux/store';
import {Theme} from '../../themes';
import {isAndroid, widthScreen} from '../../utils/Scale';
import {navigate} from '../../navigation/NavigationServices';
import {APP_SCREEN} from '../../navigation/screenType';
import TextApp from '../../components/TextApp';
import {Images} from '../../themes/images';
import Animated, {SlideInUp, SlideOutUp} from 'react-native-reanimated';
import BoardProfile from '../../components/BoardProfile.tsx';
import SvgCharacter from '../../../assets/svg/profile/SvgCharacter.tsx';
import {GridItem} from '../../components/ListViewProfile.tsx';

const ShirtsData: GridItem[] = [
  {id: 1, image: require('../../../assets/svg/profile/ao1.png'), type: 'shirt'},
  {id: 2, image: require('../../../assets/svg/profile/ao2.png'), type: 'shirt'},
  {id: 3, image: require('../../../assets/svg/profile/ao3.png'), type: 'shirt'},
  {id: 4, image: require('../../../assets/svg/profile/ao1.png'), type: 'shirt'},
  {id: 5, image: require('../../../assets/svg/profile/ao2.png'), type: 'shirt'},
  {id: 6, image: require('../../../assets/svg/profile/ao3.png'), type: 'shirt'},
];
const PantsData: GridItem[] = [
  {
    id: 1,
    image: require('../../../assets/svg/profile/quan1.png'),
    type: 'pants',
  },
  {
    id: 2,
    image: require('../../../assets/svg/profile/quan2.png'),
    type: 'pants',
  },
  {
    id: 3,
    image: require('../../../assets/svg/profile/quan1.png'),
    type: 'pants',
  },
  {
    id: 4,
    image: require('../../../assets/svg/profile/quan2.png'),
    type: 'pants',
  },
  {
    id: 5,
    image: require('../../../assets/svg/profile/quan1.png'),
    type: 'pants',
  },
  {
    id: 6,
    image: require('../../../assets/svg/profile/quan2.png'),
    type: 'pants',
  },
];
const AccessorysData: GridItem[] = [
  {
    id: 1,
    image: require('../../../assets/svg/profile/giay1.png'),
    type: 'accessory',
  },
  {
    id: 2,
    image: require('../../../assets/svg/profile/giay2.png'),
    type: 'accessory',
  },
  {
    id: 3,
    image: require('../../../assets/svg/profile/giay1.png'),
    type: 'accessory',
  },
  {
    id: 4,
    image: require('../../../assets/svg/profile/giay2.png'),
    type: 'accessory',
  },
  {
    id: 5,
    image: require('../../../assets/svg/profile/giay1.png'),
    type: 'accessory',
  },
  {
    id: 6,
    image: require('../../../assets/svg/profile/giay2.png'),
    type: 'accessory',
  },
];
const FaceData: GridItem[] = [
  {
    id: 1,
    image: require('../../../assets/svg/profile/face1.png'),
    type: 'face',
  },
  {
    id: 2,
    image: require('../../../assets/svg/profile/face2.png'),
    type: 'face',
  },
  {
    id: 3,
    image: require('../../../assets/svg/profile/face3.png'),
    type: 'face',
  },
  {
    id: 4,
    image: require('../../../assets/svg/profile/face4.png'),
    type: 'face',
  },
];
const PetData: GridItem[] = [
  {
    id: 1,
    image: require('../../../assets/svg/profile/pet1.png'),
    type: 'pets',
  },
  {
    id: 2,
    image: require('../../../assets/svg/profile/pet2.png'),
    type: 'pets',
  },
  {
    id: 3,
    image: require('../../../assets/svg/profile/pet3.png'),
    type: 'pets',
  },
  {
    id: 4,
    image: require('../../../assets/svg/profile/pet4.png'),
    type: 'pets',
  },
];
const getImageByTypeAndId = (data: GridItem[], id: number | string) => {
  return data.find(item => item.id === id)?.image;
};
const BoardName = ({name, lv}: {name: string; lv: string}) => (
  <Animated.View
    entering={SlideInUp.duration(1000)}
    exiting={SlideOutUp.duration(1000)}
    style={styles.boardNameContainer}>
    <Image
      source={Images.boardName}
      style={styles.boardNameImage}
      resizeMode="stretch"
    />
    <TextApp
      text={name}
      style={styles.boardNameText}
      preset="text_md_semibold"
      textColor="#6A2000"
    />
    <TextApp
      text={lv}
      style={styles.boardLevelText}
      preset="text_sm_semibold"
      textColor="#6A2000"
    />
  </Animated.View>
);

const Profile: React.FC = () => {
  const data = useTypedSelector(state => state.profile.data);
  const character = useTypedSelector(state => state.profile.character);

  return (
    <ImageBackground
      style={styles.container}
      source={Theme.images.bgProfile}
      resizeMode="cover">
      <Header
        title={''}
        rightIcon={<SvgIcons.Setting />}
        onPressRight={() => navigate(APP_SCREEN.SETTINGS)}
      />
      <BoardName name={data?.name || ''} lv={data?.userLevelName || ''} />
      <View style={styles.characterWrapper}>
        <SvgCharacter
          accessory={getImageByTypeAndId(AccessorysData, character.accessoryId)}
          pants={getImageByTypeAndId(PantsData, character.pantsId)}
          shirt={getImageByTypeAndId(ShirtsData, character.shirtId)}
          face={getImageByTypeAndId(FaceData, character.faceId)}
        />
        <Image
          source={getImageByTypeAndId(PetData, character.petsId)}
          style={styles.petImg}
          resizeMode={'stretch'}
        />
      </View>

      <BoardProfile
        streak="01"
        pearl={data?.coins || ''}
        exp={data?.exp || ''}
      />
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: widthScreen,
    height: '100%',
  },
  characterWrapper: {
    alignSelf: 'center',
    marginTop: verticalScale(isAndroid ? 10 : 25),
    marginBottom: 48,
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  avatar: {
    width: 117,
    height: 161,
  },
  boardNameContainer: {
    alignItems: 'center',
  },
  boardNameImage: {
    width: 220,
    height: isAndroid ? 106 : 130,
  },
  boardNameText: {
    position: 'absolute',
    bottom: isAndroid ? 20 : 32,
  },
  boardLevelText: {
    position: 'absolute',
    bottom: isAndroid ? 2 : 12,
  },

  achievementBoard: {
    width: '100%',
    flex: 1,
    marginTop: 16,
  },
  logoutWrapper: {
    position: 'absolute',
    bottom: 0,
    alignSelf: 'center',
    width: '100%',
    paddingHorizontal: moderateScale(32),
    paddingBottom: isAndroid
      ? moderateVerticalScale(16)
      : moderateVerticalScale(20),
    zIndex: 99999,
  },
  logoutButton: {
    backgroundColor: '#FF8612',
    height: 48,
    borderRadius: Theme.radius.radius_md,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Theme.colors.border,
  },
  logoutText: {
    textAlign: 'center',
    fontWeight: '600',
  },
  petImg: {
    height: 79,
    width: 64,
  },
});

export default Profile;
