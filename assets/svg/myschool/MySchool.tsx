import {
  Canvas,
  Group,
  Path,
  Text as SkiaText,
  useFont,
} from '@shopify/react-native-skia';
import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import TextApp from '../../../src/components/TextApp';
import {heightScreen, widthScreen} from '../../../src/utils/Scale';
import {ChatBubble} from './ChatBubble';
import {MarqueeView} from '../../../src/components/MarqueeView';
import {moderateVerticalScale, scale} from 'react-native-size-matters';

type Props = {
  schoolSelected: any[];
  schoolName: string;
  handleClassDetail: (isOpen: boolean) => void;
  contentBubble: {content: string; sound: string};
};

const SvgMySchool = ({
  schoolSelected,
  schoolName,
  handleClassDetail,
  contentBubble,
}: Props) => {
  const font = useFont(require('../../fonts/SFRounded-Bold.ttf'), 36);
  return (
    <View
      style={{
        width: widthScreen,
        height: heightScreen,
      }}>
      <FastImage
        source={require('./background-myschool.webp')}
        style={{
          position: 'absolute',
          width: widthScreen,
          height: heightScreen,
        }}
        resizeMode="cover"
      />

      <Canvas style={{width: widthScreen, height: heightScreen}}>
        <Group transform={[{scale: widthScreen / 1125}]}>
          <Group>
            <Path
              color="#AA1C00"
              path="M887.84 580.292c-.41 3.28-1.87 7.26-5.15 7.51-6.78.5-13.53-.87-20.3-1.36-11.35-.82-22.87.9-33.48 5.01-5.42 2.09-13.63 6.08-19.41 6.58-.61.06-1.29.06-1.74-.36-.55-.51-.49-1.38-.38-2.1 1.43-9.72 5.48-20.87 3.99-30.56-1.28-8.28-4.68-14.83-9.67-21.42-1.61-2.12-2.3-3.26-1.82-3.81.4-.53 1.84-.57 4.47-.44 4.64.24 9.25.48 13.87.57 19.16.43 38.36-.67 57.34-3.31 1.29 1.63 2.49 3.33 3.59 5.11.27.4.53.81.76 1.21a57.29 57.29 0 0 1 4.26 8.73c3.54 9.07 4.86 19 3.67 28.65v-.01Z"
            />
            <Path
              color="#D42900"
              path="M850.041 552.65c-13.36-.31-26.48-4.21-38.98-8.8-3.53-1.47-7.7-2.3-11.17-4.08.4-.53 1.84-.57 4.47-.44 4.64.24 9.25.48 13.87.57 19.16.43 38.36-.67 57.34-3.31 1.29 1.63 2.49 3.33 3.59 5.11-1.78.25-4.81 2.7-6.55 3.8-6.55 4.88-14.38 7.47-22.56 7.15h-.01Z"
            />
            <Path
              color="#D42900"
              path="M884.17 551.64c-.59 1.31-1.34 2.52-2.23 3.62-8.21 10.6-22.35 13.48-35.14 12.72-6.92.33-32.02-8.3-10.22-6.37 13.89 1.52 28.69-2.88 38.74-12.52 1.38-1.52 4.13-4.1 4.58-6.18a57.29 57.29 0 0 1 4.26 8.73h.01Z"
            />
            <Path
              color="#FFF400"
              path="m849.8 548.018 4.4 9.64 10.52-1.2c.56-.06.88.63.46 1.01l-7.8 7.16 4.4 9.64c.23.51-.33 1.03-.82.75l-9.22-5.21-7.8 7.16c-.42.38-1.08 0-.97-.55l2.11-10.38-9.22-5.21c-.49-.28-.34-1.02.22-1.09l10.52-1.2 2.11-10.38c.11-.55.87-.64 1.1-.13l-.01-.01Z"
            />
            <Path
              color="#534741"
              path="M799.61 541.1s9.66 32.81 2.44 99.72c0 0 .63 4.08 5.03 0 0 0 7.07-19.93-2.18-99.72 0 0-2.56-4.4-5.28 0h-.01Z"
            />
          </Group>

          <Path
            color="#42240A"
            path="M917.39 652.779c-5.44-2.86-11.74-3.62-18.83-4.5-8.31-1.02-16.28-1.86-23.76-4.19-6.02-1.81-11.33-4.43-16.56-6.78-12.69-5.7-27.47-9.48-42.01-11.89-22.47-3.71-438.71-5-461.38-2.51-20.28 2.23-41.28 6.19-59.15 14-5.66 2.52-11.39 5.36-17.97 7.21-7.21 2.1-14.8 2.86-22.68 3.81-7.1.85-13.51 1.71-18.67 4.43-17.23 9.34-27.36 64.88-26.72 81.18.01 6.72 4.08 11.8 13.93 12.77 47.01 1.13 490.7-.65 538.02-.51 52.46.02 105.19 1.08 157.95 1.53 11.49.32 22.33-1.47 23.75-9.62 2.91-15.93-8.91-75.36-25.76-84.83l-.16-.09v-.01Z"
          />
          <Path
            color="#FFF9D6"
            path="M927.84 716.873c-.08 2.96-.35 5.42-.85 7.19-1.72 6.47-11.58 7.88-21.91 7.63-47.47-.36-94.93-1.21-142.18-1.23-42.63-.12-481.47 1.29-523.84.38-8.93-.77-12.87-4.8-13.27-10.16-.08-.68-.14-1.46-.18-2.32-.65-15.49 5.67-57.06 20.39-64.54 4.68-2.31 10.62-3.05 17.21-3.76 7.32-.81 14.39-1.46 21.05-3.25 6.09-1.57 11.37-4.01 16.59-6.17 16.56-6.73 36.25-10.15 55.34-12.08 21.37-2.17 436.44-1.04 457.58 2.19 13.66 2.07 27.46 5.35 39.22 10.26 4.84 2.02 9.72 4.26 15.29 5.81 6.91 1.99 14.32 2.71 22.05 3.58 6.58.76 12.42 1.4 17.36 3.83l.15.07c13.07 6.9 20.45 44.21 19.99 62.57h.01Z"
          />
          {font && (
            <SkiaText
              x={580 - font.measureText(schoolName).width / 2}
              y={698}
              text={schoolName}
              font={font}
              color="#3A3A3A"
            />
          )}
          <Path
            color="#E2D5B3"
            path="M927.84 716.871c-.08 2.96-.35 5.42-.85 7.19-1.72 6.47-11.58 7.88-21.91 7.63-47.47-.36-94.93-1.21-142.18-1.23-42.63-.12-481.47 1.29-523.84.38-8.93-.77-12.87-4.8-13.27-10.16-.08-.68-.14-1.46-.18-2.32 3.32 3.24 7.89 5.19 12.55 6.17 5.09 1.06 10.36 1.05 15.57 1.04 19.47-.06 38.95-.11 58.44-.17 12.41-.04 24.82-.07 37.23-.1 51.59-.15 498.78-.29 550.38-.44 5.75 0 11.5-.03 17.22-.62 3-.31 5.15-2.76 7.26-4.81 1.02-.99 2.25-1.95 3.57-2.56h.01Z"
          />
          <Path
            color="#42240A"
            path="M232.39 742.43c-1.03-2.38-4.87-4.13-5.32-7.36-.17-28.87 5.45-55.55 12.9-78.23.38-1.07.81-2.12 1.4-3.17 1.26-2.76 5.57-5.88 3.92-7.89-2.71-1.9-11.12-.02-15.47 1.13-2.85.82-5.92 2.44-7.24 4.51-1.01 1.47-1.26 3-1.33 4.44-.02 3.21-.14 6.5-.98 10.05-4.78 21.32-12.77 46.28-20.41 74.91-1.42 4.94-1.62 10.31 3.46 10.95 2.6.46 5.75.03 8.72-.36 4.55-.84 21.09-1.61 20.38-8.84l-.04-.12.01-.02Z"
          />
          <Path
            color="#42240A"
            path="M920.15 742.97c1.05-2.38 4.91-4.12 5.38-7.35.33-28.91-5.17-55.65-12.51-78.38-.38-1.08-.8-2.13-1.39-3.18-1.25-2.77-5.55-5.9-3.89-7.92 2.73-1.89 11.15.01 15.5 1.18 2.86.83 5.92 2.46 7.23 4.53 1 1.47 1.24 3.01 1.31 4.45 0 3.21.1 6.51.93 10.06 4.68 21.37 12.56 46.39 20.07 75.1 1.4 4.95 1.57 10.34-3.53 10.95-2.61.45-5.76 0-8.75-.4-4.56-.86-21.14-1.7-20.4-8.93l.04-.12.01.01Z"
          />
          <Path
            color="#301F00"
            path="M203.66 747.371c-1.45-1.09-1.41-3.13-.73-4.66 2.34-5.82 14.54-9.58 20.5-8.11 4.74 1.15 6.64 6.27 1.81 8.87-4.77 2.41-16.8 6.8-21.47 3.99l-.12-.09h.01Z"
          />
          <Path
            color="#301F00"
            path="M948.45 747.371c1.45-1.09 1.41-3.13.73-4.66-2.34-5.82-14.54-9.58-20.5-8.11-4.74 1.15-6.64 6.27-1.81 8.87 4.77 2.41 16.8 6.8 21.47 3.99l.12-.09h-.01Z"
          />

          <Path
            color="#E2D5B3"
            path="M526.49 626.742c1.59 9.11 7.01 17.48 14.66 22.66s17.43 7.09 26.46 5.18c4.54-.96 8.87-2.83 13.46-3.5 11.45-1.67 22.81 4.29 34.37 3.64 7.99-.46 15.74-4.24 21.02-10.27 5.28-6.03 8.02-14.22 7.43-22.21-9 9.42-22.47 13.47-35.49 13.76-13.02.29-25.84-2.79-38.54-5.69-12.69-2.9-25.63-5.67-43.38-3.56l.01-.01Z"
          />

          <Path
            color="#D36C28"
            path="M643.84 612.79c-1.87-.74-1.58.671-2.59 2.541-9 16.57-30.23 25.78-46.4 18.2-2.85-1.33-5.62-3.09-8.73-3.39-6.17-.59-11.42 4.57-16.98 7.61-13.72 7.5-32.45.359-38.43-14.641l8.05 9.43c-2.68-3.62-3.54-8.72-2.22-13.18-3.45-1.77-7.96.291-9.91 3.881-1.96 3.59-1.67 8.189-.06 11.919 3.58 8.27 12.76 12.251 21.31 12.101 8.55-.15 16.75-3.621 24.86-6.651 1.83-.68 3.67-1.35 5.58-1.63 8.54-1.25 16.09 5.151 24.18 8.141 9.23 3.41 19.92 2.169 28.4-3.271 8.48-5.45 14.6-15.009 16.27-25.419.3-1.85-1.69-4.99-3.32-5.63l-.01-.011Z"
          />
          <Path
            color="#fff"
            path="M639.8 626.789c-3.14 7.27-12.65 11.96-22.67 13.92-8.69 1.69-17.75 1.33-23.34-1.17-2.83-1.18-5.23-3.36-7.48-5.42-.95-.94-1.96-1.38-2.99-1.49-1.04-.1-2.11.16-3.16.68-.54.27-1.09.6-1.62.98-8.6 5.9-21.87 10.04-32.76 7.75-3.53-.75-6.8-2.16-9.59-4.39-5.74-4.99-6.49-14-2.6-20.45 5.84-9.4 16.48-16.52 27.25-14.35 3.93.68 7.7 2.35 10.92 4.83 1.82 1.18 4.02 4.3 5.84 4.41.73-.02 1.3-.82 1.9-2.14.68-1.52 1.47-3.6 2.59-5.28 1.79-2.78 4.22-5.17 6.93-6.94 12.89-8.51 35.8-3.22 46.33 7.37l.18.19c5.06 5.37 7.68 14.26 4.27 21.5Z"
          />
          <Path
            color="#000"
            path="M580.17 633.31c-.54.27-1.09.6-1.62.98-8.6 5.9-21.87 10.04-32.76 7.75a16.891 16.891 0 0 1-2.35-9.84c.66-9.74 9.4-17.13 19.53-16.52 9.74.59 17.23 8.36 17.19 17.63h.01Z"
          />
          <Path
            color="#000"
            path="M620 632.338c-.21 3.09-1.23 5.94-2.86 8.38-8.69 1.69-17.75 1.33-23.34-1.17-2.83-1.18-5.23-3.36-7.48-5.42-.95-.94-1.96-1.38-2.99-1.49-.07-.82-.08-1.66-.02-2.51.66-9.74 9.4-17.14 19.53-16.53 10.12.61 17.81 9 17.16 18.74Z"
          />
          <Path
            color="#fff"
            path="M574.311 625.479c.227-1.992-1.089-3.777-2.938-3.987-1.849-.211-3.532 1.234-3.759 3.225-.226 1.992 1.089 3.778 2.939 3.988 1.849.21 3.532-1.234 3.758-3.226Z"
          />
          <Path
            color="#fff"
            path="M594.211 623.85c.226-1.992-1.089-3.777-2.938-3.987-1.85-.211-3.533 1.234-3.759 3.226-.227 1.992 1.089 3.777 2.938 3.987 1.849.211 3.532-1.234 3.759-3.226Z"
          />
          <Path
            color="#F9A743"
            path="M575.51 600.529c4.66-14.2 20.53-21.73 34.3-19.48 13.77 2.25 25.36 12.21 34.09 23.75 1.89 2.5 3.75 5.98 2.17 8.78-1.25 2.22-4.19 2.72-6.45 1.9-2.26-.82-4.07-2.62-5.95-4.2-8.26-6.96-19.2-10.17-29.73-8.74-10.52 1.43-20.5 7.5-27.12 16.49-3.96-7.81-11.69-11.21-19.95-12.26-6.55-.83-13.91 1.35-17.75 7.19-1.49 2.26-2.38 4.94-3.99 7.11-1.61 2.17-4.35 3.8-6.71 2.78-3.14-12.68 3.4-27.46 14.62-33.02 11.22-5.55 25.76-1.21 32.47 9.7Z"
          />
        </Group>
        <Path
          color="#000"
          path="M625.1 589.931c3.25-4.11-6.17-12.33-9.55-13.97-8.06-4.07-20.28-1.62-26.24 5.65-2.52 2.84-5.3 7.69-3.86 11.08 1.92 2.99 4.77-1.88 6.29-3.6 5.1-7.04 14.98-8.24 21.9-4.11 3.36 1.65 7.59 6.83 11.38 5.04l.09-.08-.01-.01Z"
        />
        <Path
          color="#000"
          path="M533.17 605.221c-4.18-2.84 2.69-13.84 5.49-16.54 6.64-6.57 18.96-8.31 26.55-3.34 3.15 1.88 7.07 5.58 6.58 9.3-1.05 3.5-5.04-.2-6.95-1.35-6.71-5.03-16.46-2.89-22 3.37-2.78 2.69-5.48 9.06-9.56 8.6l-.11-.04Z"
        />
      </Canvas>
      {schoolSelected.map((item, index) => {
        let source = item.source;

        const classStatus = item?.info?.classStatus;
        const isOpen = item?.isOpen && classStatus === 1;

        if (!source) {
          if (item.type === 'top') {
            source = isOpen
              ? require('./classroom-open.webp')
              : require('./classroom-closed.webp');
          } else if (item.type === 'floor1') {
            source = isOpen
              ? require('./classroom-open-floor1.webp')
              : require('./classroom-closed-floor1.webp');
          }
        }

        return (
          <TouchableOpacity
            key={item.id}
            style={{
              position: 'absolute',
              left: (item.left / 1125) * widthScreen,
              top: (item.top / 2436) * heightScreen,
              width: (item.width / 1125) * widthScreen,
              height: (item.height / 2436) * heightScreen,
              zIndex: 1,
            }}
            disabled={!item?.isOpen && item?.type !== 'fixed'}
            onPress={handleClassDetail?.bind(null, item)}>
            <FastImage
              source={source}
              style={{width: '100%', height: '100%'}}
              resizeMode="contain"
            />
            {item?.type !== 'fixed' && (
              <View
                style={{
                  position: 'absolute',
                  left: item?.type === 'top' ? 25 : 35,
                  right: 0,
                  top: `3%`,
                  width: scale(35),
                  justifyContent: 'center',
                  alignItems: 'center',
                  zIndex: 1,
                }}>
                <MarqueeView>
                  <TextApp
                    preset="text_xs_bold"
                    text={item?.className}
                    textColor="#F9EFC5"
                    style={{lineHeight: 18}}
                  />
                </MarqueeView>
              </View>
            )}
          </TouchableOpacity>
        );
      })}

      <View
        style={{
          position: 'absolute',
          left: (50 / 1125) * widthScreen,
          bottom: (450 / 2436) * heightScreen,
          width: 121,
          height: 161,
          zIndex: 10,
        }}>
        <FastImage
          source={require('./student-character.webp')}
          style={{width: '100%', height: '100%'}}
          resizeMode="contain"
        />
      </View>
      <ChatBubble contentBubble={contentBubble} />
    </View>
  );
};
export default SvgMySchool;
