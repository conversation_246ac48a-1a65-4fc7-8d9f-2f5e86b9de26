import * as React from 'react';
import {Pressable, View} from 'react-native';
import Svg, {G, Image, Text} from 'react-native-svg';
import FastImage from 'react-native-fast-image';
import {FontFamily} from '../../../src/themes/typography';
import {heightScreen, widthScreen} from '../../../src/utils/Scale';
import {MissionCompletedItem} from './MissionCompletedItem';
import {navigate} from '../../../src/navigation/NavigationServices';
import {APP_SCREEN} from '../../../src/navigation/screenType';
import {moderateVerticalScale, scale} from 'react-native-size-matters';

type MissionItem = {
  y: number;
};

type Props = {
  className: string;
  teacher: string;
  numberStudent: string;
  joinSince: string;
  missionData: any[];
  classId: string;
};

const mission: MissionItem[] = [{y: 317.765}, {y: 394.765}, {y: 470.765}];

const SvgYourClass = ({
  className,
  teacher,
  numberStudent,
  joinSince,
  missionData,
  classId,
}: Props) => {
  const updatedMissionData = missionData?.map((item, index) => ({
    ...item,
    y: mission[index]?.y ?? 0,
  }));

  const handleGoToMissionDetail = (missionId: string) => {
    navigate(APP_SCREEN.LESSON, {unitId: missionId});
  };

  const handleClassLeaderBoard = () => {
    navigate(APP_SCREEN.CLASS_LEADERBOARD, {classId});
  };

  return (
    <View
      style={{
        width: widthScreen,
        height: heightScreen,
      }}>
      <FastImage
        source={require('./bg-your-class.webp')}
        style={{
          position: 'absolute',
          width: widthScreen,
          height: heightScreen,
        }}
        resizeMode="cover"
      />
      <Svg
        width={'100%'}
        height={'100%'}
        viewBox="0 0 375 812"
        preserveAspectRatio="none"
        fill="none">
        <G id="Your Class Information" clipPath="url(#clip0_1_1036)">
          <G id="Frame 427320187">
            <G id="Frame 427320223">
              <G id="Frame 427320225">
                <G id="class-information">
                  <Image
                    x={43.727}
                    y={79.99}
                    id="image0_367_1053"
                    width={296}
                    height={67.82}
                    preserveAspectRatio="none"
                    href={require('./your-class-infor.webp')}
                  />
                </G>
                <G id="class-detail">
                  <Image
                    x={53.727}
                    y={148.368}
                    id="image0_367_1053"
                    width={272.963}
                    height={120.368}
                    preserveAspectRatio="none"
                    href={require('./bg-class-infor.webp')}
                  />
                  <G id="Frame 427320188">
                    <Text
                      x={187.47}
                      y={187.635}
                      fontWeight={'bold'}
                      textAnchor="middle"
                      fontSize={30}
                      fontFamily={FontFamily.bold}
                      fill={'#6A2600'}>
                      {className}
                    </Text>
                    <G id="Frame 427320182">
                      <Text
                        x={73.97}
                        y={210.731}
                        fontWeight={'normal'}
                        textAnchor="start"
                        fontSize={14}
                        fontFamily={FontFamily.regular}
                        fill={'#414651'}>
                        Teacher:
                      </Text>
                      <Text
                        x={301.971}
                        y={210.731}
                        fontWeight={'normal'}
                        textAnchor="end"
                        fontSize={14}
                        fontFamily={FontFamily.regular}
                        fill={'#414651'}>
                        {teacher}
                      </Text>
                    </G>
                    <G id="Frame 427320185">
                      <Text
                        x={73.97}
                        y={230.731}
                        fontWeight={'normal'}
                        textAnchor="start"
                        fontSize={14}
                        fontFamily={FontFamily.regular}
                        fill={'#414651'}>
                        Number of students:
                      </Text>
                      <Text
                        x={301.971}
                        y={230.731}
                        fontWeight={'normal'}
                        textAnchor="end"
                        fontSize={14}
                        fontFamily={FontFamily.regular}
                        fill={'#414651'}>
                        {numberStudent}
                      </Text>
                    </G>
                    <G id="Frame 427320186">
                      <Text
                        x={73.97}
                        y={250.799}
                        fontWeight={'normal'}
                        textAnchor="start"
                        fontSize={14}
                        fontFamily={FontFamily.regular}
                        fill={'#414651'}>
                        Joined since:
                      </Text>
                      <Text
                        x={301.971}
                        y={250.799}
                        fontWeight={'normal'}
                        textAnchor="end"
                        fontSize={14}
                        fontFamily={FontFamily.regular}
                        fill={'#414651'}>
                        {joinSince}
                      </Text>
                    </G>
                  </G>
                </G>
              </G>
              <G id="mission-complete">
                <Image
                  x={53.727}
                  y={275.999}
                  id="image0_367_1053"
                  width={272.963}
                  height={273.783}
                  preserveAspectRatio="none"
                  href={require('./bg-mission-infor.webp')}
                />
                <G id="Frame 427320192">
                  <G id="Frame 427320224">
                    <Text
                      x={190.597}
                      y={302.783}
                      fontWeight={'bold'}
                      textAnchor="middle"
                      fontSize={16}
                      fontFamily={FontFamily.bold}
                      fill={'#6A2600'}>
                      Mission need to be completed
                    </Text>
                  </G>
                  {updatedMissionData?.map((item, index) => (
                    <MissionCompletedItem
                      key={index}
                      id={item?.id}
                      y={item?.y}
                      missionName={item?.name}
                      numOfPass={item?.numOfPass}
                      numOfQuestion={item?.numOfQuestion}
                    />
                  ))}
                </G>
              </G>
            </G>
            <G id="class-leaderboard">
              <G id="Group 14358">
                <Image
                  x={79.59}
                  y={555.622}
                  id="image0_367_1053"
                  width={218.27}
                  height={48}
                  preserveAspectRatio="none"
                  href={require('./class-leaderboard.webp')}
                />
              </G>
            </G>
          </G>
        </G>
      </Svg>
      {updatedMissionData?.map((item, index) => (
        <Pressable
          key={index}
          onPress={handleGoToMissionDetail?.bind(null, item?.exAssignId)}
          style={{
            width: scale(232),
            height: moderateVerticalScale(60),
            top: ((item.y + 5) / 812) * heightScreen,
            left: (65 / 375) * widthScreen,
            position: 'absolute',
            zIndex: 1,
          }}
        />
      ))}
      <Pressable
        style={{
          width: scale(218.27),
          height: moderateVerticalScale(48),
          top: (555.622 / 812) * heightScreen,
          left: (79.59 / 375) * widthScreen,
          position: 'absolute',
          zIndex: 1,
        }}
        onPress={handleClassLeaderBoard}
      />
    </View>
  );
};
export default SvgYourClass;
